<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Third Party Results - Fact Check Platform</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2rem;
            font-weight: 600;
        }
        .header p {
            margin: 10px 0 0;
            opacity: 0.9;
        }
        .content {
            padding: 30px;
        }
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .test-section h2 {
            margin: 0 0 20px;
            color: #495057;
            font-size: 1.25rem;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #495057;
        }
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #dee2e6;
            border-radius: 6px;
            font-size: 16px;
            box-sizing: border-box;
        }
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.2s;
        }
        .btn:hover {
            background: #5a67d8;
        }
        .btn:disabled {
            background: #a0aec0;
            cursor: not-allowed;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #667eea;
        }
        .results {
            margin-top: 30px;
        }
        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .result-card {
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            padding: 20px;
            background: white;
        }
        .result-card h3 {
            margin: 0 0 15px;
            color: #495057;
            font-size: 1.1rem;
        }
        .third-party-list {
            space-y: 8px;
        }
        .third-party-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 6px;
            margin-bottom: 8px;
        }
        .service-name {
            font-weight: 500;
            color: #495057;
        }
        .status-clean {
            color: #28a745;
            font-weight: 600;
        }
        .status-suspicious {
            color: #ffc107;
            font-weight: 600;
        }
        .status-malicious {
            color: #dc3545;
            font-weight: 600;
        }
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
        }
        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Test Third Party Results Integration</h1>
            <p>Kiểm tra tính năng "Kết quả từ bên thứ 3" đã được khắc phục</p>
        </div>
        
        <div class="content">
            <!-- Test Section 1: Sample Results -->
            <div class="test-section">
                <h2>📊 Test 1: Sample Third Party Results</h2>
                <p>Kiểm tra danh sách 12 dịch vụ bảo mật:</p>
                <button class="btn" onclick="loadSampleResults()">Tải mẫu kết quả</button>
                <div id="sampleResults"></div>
            </div>
            
            <!-- Test Section 2: Full Link Check -->
            <div class="test-section">
                <h2>🔗 Test 2: Complete Link Check</h2>
                <p>Kiểm tra link đầy đủ với tất cả tính năng:</p>
                
                <div class="form-group">
                    <label for="testUrl">Nhập URL để kiểm tra:</label>
                    <input type="text" id="testUrl" value="https://example.com" placeholder="https://example.com">
                </div>
                
                <button class="btn" onclick="checkLink()" id="checkBtn">Kiểm tra Link</button>
                <div id="linkResults"></div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = window.location.origin;
        
        async function loadSampleResults() {
            const resultsDiv = document.getElementById('sampleResults');
            resultsDiv.innerHTML = '<div class="loading">Đang tải mẫu kết quả...</div>';
            
            try {
                const response = await fetch(`${API_BASE}/api/links/test-third-party`);
                const data = await response.json();
                
                if (data.success) {
                    const html = `
                        <div class="success-message">
                            ✅ Thành công! Đã tạo ${data.data.count} dịch vụ bên thứ 3
                        </div>
                        <div class="result-card">
                            <h3>🛡️ Kết quả từ bên thứ 3 (${data.data.count} dịch vụ)</h3>
                            <div class="third-party-list">
                                ${data.data.services.map(service => `
                                    <div class="third-party-item">
                                        <span class="service-name">${service.name}</span>
                                        <span class="status-${service.status}">${service.details}</span>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    `;
                    resultsDiv.innerHTML = html;
                } else {
                    resultsDiv.innerHTML = `<div class="error-message">❌ Lỗi: ${data.error}</div>`;
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error-message">❌ Không thể kết nối đến server: ${error.message}</div>`;
            }
        }
        
        async function checkLink() {
            const url = document.getElementById('testUrl').value;
            const resultsDiv = document.getElementById('linkResults');
            const btn = document.getElementById('checkBtn');
            
            if (!url) {
                alert('Vui lòng nhập URL');
                return;
            }
            
            btn.disabled = true;
            btn.textContent = 'Đang kiểm tra...';
            resultsDiv.innerHTML = '<div class="loading">Đang phân tích link và kiểm tra bảo mật...</div>';
            
            try {
                const response = await fetch(`${API_BASE}/api/links/test-check`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ url })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    const result = data.result;
                    const statusClass = result.status === 'safe' ? 'status-clean' : 
                                      result.status === 'warning' ? 'status-suspicious' : 'status-malicious';
                    
                    const html = `
                        <div class="success-message">
                            ✅ Kiểm tra hoàn tất! Đã tìm thấy ${result.thirdPartyResults?.length || 0} dịch vụ bên thứ 3
                        </div>
                        <div class="results-grid">
                            <div class="result-card">
                                <h3>📊 Kết quả tổng quan</h3>
                                <p><strong>URL:</strong> ${result.url}</p>
                                <p><strong>Trạng thái:</strong> <span class="${statusClass}">${result.status}</span></p>
                                <p><strong>Điểm tổng:</strong> ${result.finalScore}/100</p>
                                <p><strong>Điểm tin cậy:</strong> ${result.credibilityScore}/100</p>
                                <p><strong>Điểm bảo mật:</strong> ${result.securityScore}/100</p>
                            </div>
                            
                            <div class="result-card">
                                <h3>🛡️ Kết quả từ bên thứ 3</h3>
                                ${result.thirdPartyResults && result.thirdPartyResults.length > 0 ? `
                                    <div class="third-party-list">
                                        ${result.thirdPartyResults.map(service => `
                                            <div class="third-party-item">
                                                <span class="service-name">${service.name}</span>
                                                <span class="status-${service.status}">${service.details}</span>
                                            </div>
                                        `).join('')}
                                    </div>
                                ` : '<p>Không có dữ liệu từ bên thứ 3</p>'}
                            </div>
                        </div>
                    `;
                    resultsDiv.innerHTML = html;
                } else {
                    resultsDiv.innerHTML = `<div class="error-message">❌ Lỗi: ${data.error}</div>`;
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error-message">❌ Không thể kiểm tra link: ${error.message}</div>`;
            } finally {
                btn.disabled = false;
                btn.textContent = 'Kiểm tra Link';
            }
        }
        
        // Auto-load sample results on page load
        window.addEventListener('load', () => {
            setTimeout(loadSampleResults, 500);
        });
    </script>
</body>
</html>
