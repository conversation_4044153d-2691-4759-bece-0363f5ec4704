<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Frontend Integration</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            background: #f8f9fa;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .loading {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .third-party-item {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .status-clean { color: #28a745; font-weight: bold; }
        .status-suspicious { color: #ffc107; font-weight: bold; }
        .status-malicious { color: #dc3545; font-weight: bold; }
        .json-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Frontend Integration - Third Party Results</h1>
        <p>Test để xem frontend có nhận được đúng data structure từ backend không</p>

        <div class="test-section">
            <h2>📊 Test: Simulate Frontend API Call</h2>
            <p>Test giống như React frontend sẽ gọi API</p>
            <input type="text" id="testUrl" value="https://fb.com" placeholder="Enter URL">
            <button class="btn" onclick="testFrontendAPICall()">Test Frontend API Call</button>
            <div id="result" class="result"></div>
        </div>

        <div class="test-section">
            <h2>🔍 Raw API Response</h2>
            <p>Xem raw response từ backend</p>
            <div id="rawResponse" class="json-display"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000';

        async function testFrontendAPICall() {
            const url = document.getElementById('testUrl').value;
            const resultDiv = document.getElementById('result');
            const rawDiv = document.getElementById('rawResponse');
            
            if (!url) {
                alert('Please enter URL');
                return;
            }

            resultDiv.innerHTML = '<div class="loading">Testing frontend API call strategy...</div>';
            rawDiv.innerHTML = '';

            try {
                // Simulate frontend API call strategy
                console.log('🔍 Starting frontend API call simulation...');
                
                // Strategy 1: Try test endpoint first (like updated frontend)
                console.log('🧪 Trying test endpoint first...');
                const testResponse = await fetch(`${API_BASE}/api/test/check-link`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ url })
                });

                if (testResponse.ok) {
                    const data = await testResponse.json();
                    console.log('✅ Test endpoint success:', data);
                    
                    // Display raw response
                    rawDiv.innerHTML = JSON.stringify(data, null, 2);
                    
                    if (data.success && data.result) {
                        const result = data.result;
                        
                        // Check if thirdPartyResults exists
                        const hasThirdParty = result.thirdPartyResults && result.thirdPartyResults.length > 0;
                        
                        const html = `
                            <div class="success">
                                ✅ Frontend API Call Success!
                                <br><strong>Strategy:</strong> Test endpoint (no auth required)
                                <br><strong>URL:</strong> ${result.url}
                                <br><strong>Status:</strong> ${result.status}
                                <br><strong>Final Score:</strong> ${result.finalScore}/100
                                <br><strong>Security Score:</strong> ${result.securityScore}/100
                                <br><strong>Credibility Score:</strong> ${result.credibilityScore}/100
                                
                                <h4>🛡️ Third Party Results: ${hasThirdParty ? '✅ FOUND' : '❌ MISSING'}</h4>
                                ${hasThirdParty ? `
                                    <p><strong>Count:</strong> ${result.thirdPartyResults.length} services</p>
                                    <div style="max-height: 200px; overflow-y: auto;">
                                        ${result.thirdPartyResults.map((service, index) => `
                                            <div class="third-party-item">
                                                <span>${index + 1}. ${service.name}</span>
                                                <span class="status-${service.status}">${service.details}</span>
                                            </div>
                                        `).join('')}
                                    </div>
                                ` : '<p style="color: red;">❌ No third party results found in response!</p>'}
                                
                                <h4>📸 Screenshot Check:</h4>
                                ${result.screenshot ? `
                                    <div style="margin: 10px 0;">
                                        <img src="${result.screenshot}" alt="Website Screenshot"
                                             style="max-width: 300px; border: 1px solid #ddd; border-radius: 4px;"
                                             onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                        <div style="display: none; padding: 20px; background: #f8f9fa; border: 1px solid #ddd; border-radius: 4px; text-align: center;">
                                            Screenshot not available
                                        </div>
                                    </div>
                                    <p><strong>Screenshot URL:</strong> <a href="${result.screenshot}" target="_blank">${result.screenshot}</a></p>
                                ` : '<p style="color: red;">❌ No screenshot URL found</p>'}

                                <h4>📋 Data Structure Check:</h4>
                                <ul>
                                    <li>result.url: ${result.url ? '✅' : '❌'}</li>
                                    <li>result.status: ${result.status ? '✅' : '❌'}</li>
                                    <li>result.finalScore: ${result.finalScore !== undefined ? '✅' : '❌'}</li>
                                    <li>result.screenshot: ${result.screenshot ? '✅' : '❌'}</li>
                                    <li>result.thirdPartyResults: ${result.thirdPartyResults ? '✅' : '❌'}</li>
                                    <li>result.thirdPartyResults.length: ${result.thirdPartyResults?.length || 0}</li>
                                    <li>result.screenshotInfo: ${result.screenshotInfo ? '✅' : '❌'}</li>
                                </ul>
                            </div>
                        `;
                        resultDiv.innerHTML = html;
                    } else {
                        resultDiv.innerHTML = `<div class="error">❌ Invalid response structure: ${JSON.stringify(data)}</div>`;
                    }
                } else {
                    // Try authenticated endpoint
                    console.log('🔐 Test endpoint failed, trying authenticated...');
                    resultDiv.innerHTML = `<div class="error">❌ Test endpoint failed with status: ${testResponse.status}</div>`;
                }

            } catch (error) {
                console.error('❌ Frontend API call failed:', error);
                resultDiv.innerHTML = `<div class="error">❌ Frontend API Error: ${error.message}</div>`;
                rawDiv.innerHTML = `Error: ${error.message}`;
            }
        }

        // Auto-run test on page load
        window.addEventListener('load', () => {
            console.log('🚀 Page loaded, running auto-test...');
            setTimeout(testFrontendAPICall, 1000);
        });
    </script>
</body>
</html>
